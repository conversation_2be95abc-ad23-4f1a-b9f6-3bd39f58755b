<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
	<%@page import="org.changneng.framework.frameworkcore.utils.PropertiesHandlerUtil"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<%
	String fastdfs_addr=PropertiesHandlerUtil.getValue("fastdfs.nginx.ip","fastdfs");
%>
<c:set var="FASTDFS_ADDR"><%=fastdfs_addr%></c:set>
<%
  String server_addr=PropertiesHandlerUtil.getValue("server.addr","fastdfs");
%>
<c:set var="SERVER_ADDR"><%=server_addr%></c:set>

<script type="text/javascript">
	var SERVER_ADDR='${SERVER_ADDR}';
	var FASTDFS_ADDR = '${FASTDFS_ADDR}';
</script>

<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Insert title here</title>
</head>
<style>
/*
PDFObject appends the classname "pdfobject-container" to the target element.
This enables you to style the element differently depending on whether the embed was successful.
In this example, a successful embed will result in a large box.
A failed embed will not have dimensions specified, so you don't see an oddly large empty box.
*/
.pdfobject-container {
	width: 100%;
	height:580px;
	margin: 2em 0;
}

/* 环境监管一件事表单样式 */
#envSupervisionReadOnlyForm .panel-group {
    margin-bottom: 0;
}

#envSupervisionReadOnlyForm .panel {
    border: none;
    border-radius: 0;
    box-shadow: none;
    margin-bottom: 18px;
}

#envSupervisionReadOnlyForm .panel-heading {
    background-color: #ffffff;
    border: 1px solid #e9ecef;
    border-left: 4px solid #17a2b8;
    border-radius: 0;
    padding: 0;
    position: relative;
}

#envSupervisionReadOnlyForm .panel-title {
    margin: 0;
    font-size: 14px;
}

#envSupervisionReadOnlyForm .panel-title a {
    display: block;
    padding: 12px 15px;
    color: #333;
    text-decoration: none;
    font-size: 16px;
    position: relative;
    cursor: default;
}

#envSupervisionReadOnlyForm .panel-title a:hover,
#envSupervisionReadOnlyForm .panel-title a:focus {
    text-decoration: none;
    color: #333;
    background-color: #f1f3f4;
}

#envSupervisionReadOnlyForm .panel-title .collapse-icon {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #17a2b8;
    font-size: 16px;
    transition: transform 0.3s ease;
}

#envSupervisionReadOnlyForm .panel-title a[aria-expanded="true"] .collapse-icon {
    transform: translateY(-50%) rotate(180deg);
}

#envSupervisionReadOnlyForm .panel-collapse {
    border-top: none;
}

#envSupervisionReadOnlyForm .panel-body {
    padding: 0;
    background-color: #fff;
}
</style>
<body>
	<div class="main-container">
		<div class="padding-md">
		<jsp:include page="BusinessFlow.jsp"></jsp:include>
			<div class="row">
				<div class="col-lg-12">
					<div class="smart-widget widget-blue">
						<div class="smart-widget-header font-16">
							<i class="fa fa-comment"></i> 现场执法 <span
								class="smart-widget-option" style="margin-top: -7px;"> <span
								class="refresh-icon-animated"><i
									class="fa fa-circle-o-notch fa-spin"></i></span>
							</span>
						</div>
							<input type="hidden" id ='lawObjectType' name ="lawObjectType" value ="${lawObj.lawObjectType}">
                            <input id="taskId" type="hidden" name="taskId" value="${lawObj.taskId}"></input>
						    <input id="localCheakId" type="hidden" name="id" value="${localCheak.id}"></input>
                            <div class="smart-widget-inner table-responsive">
                                <div class="smart-widget-body form-horizontal">
                                      <div class="form-group">
                                                <label class="col-lg-2 col-sm-2 col-xs-5 control-label"> <span style="color: red;">*</span> 执法对象名称</label>
                                                    <div class="col-lg-8 col-sm-8 col-xs-12">
                                                <div class="input-group" style="margin-top:7px;">
                                                <span>${localCheak.objectName }</span>
                                                </div>
                                            </div>
                                      </div>
                                            <div class="form-group">
                                                <label class="col-lg-2 col-sm-2 col-xs-5 control-label">详细地址</label>
                                                    <div class="col-lg-8 col-sm-8 col-xs-12" style ="margin-top:8px;">
                                                    <span>${localCheak.address }</span>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-lg-2 col-sm-2 col-xs-5 control-label">法人代表</label>
                                                <div class="col-lg-8 col-sm-8 col-xs-12" style="margin-top:7px;">
                                                    <span>${localCheak.legalPerson }</span>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                            <label class="col-lg-2 col-sm-2 col-xs-5 control-label">法人电话</label>
                                                <div class="col-lg-8 col-sm-8 col-xs-12" style="margin-top:7px;">
                                                <span>${localCheak.legalPhone }</span>
                                                </div>
                                            </div>
                                        <input id="localCheakId" type="hidden" name="id"
										value="${localCheak.id}">
                                <div class="form-group">
                                    <label for="检查人" class="col-lg-2 col-sm-2 col-xs-5 control-label"><span
                                        style="color: red;">*</span> 检查人</label>
                                    <div class="col-lg-8 col-sm-8 col-xs-12">
                                        <div class="input-group" style="margin-top:7px;">
                                         <span>${localCheak.checkUserNames}</span>
                                            </div>
                                        </div>
                                    </div>

                                <div class="form-group">
                                    <label for="执法证号" class="col-lg-2 col-sm-2 col-xs-5 control-label"><span
                                        style="color: red;">*</span> 执法证号</label>
                                    <div class="col-lg-8 col-sm-8 col-xs-12" style="margin-top:7px;">
                                     <span>${localCheak.lawEnforcIds}</span>
                                    </div>
                                </div>
								<div class="form-group">
                                    <label for="记录人" class="col-lg-2 col-sm-2 col-xs-5 control-label"><span
                                        style="color: red;">*</span> 记录人</label>
                                    <div class="col-lg-8 col-sm-8 col-xs-12" style="margin-top:7px;">
                                     <span>${localCheak.recordUserName}</span>
                                    </div>
                                </div>
                                <div class="form-group">
									<label for="参与人员及其工作单位" class="col-lg-2 col-sm-2 col-xs-5 control-label">参与人员及其工作单位</label>
									<div class="col-lg-8 col-sm-8 col-xs-12" style="margin-top:7px;">
										<span>${localCheak.participant }</span>
									</div>
								</div>
                                        <!-- 检查人的信息-->
                                              <div class="form-group">
                                                <label for="被检查单位现场负责人" class="col-lg-2 col-sm-2 col-xs-5 control-label">被检查单位现场负责人</label>
                                                <div class="col-lg-8 col-sm-8 col-xs-12" style="margin-top:7px;">
                                                 <span>${localCheak.localPerson}</span>
                                                </div>
                                            </div>

                                            <div class="form-group">
                                             <label for="现场负责人电话" class="col-lg-2 col-sm-2 col-xs-5 control-label">现场负责人电话</label>
                                                <div class="col-lg-8 col-sm-8 col-xs-12" style="margin-top:7px;">
                                                 <span>${localCheak.localPersonPhone}</span>
                                                </div>
                                            </div>
                                             <div class="form-group">
                                                <label for="职务" class="col-lg-2 col-sm-2 col-xs-5 control-label">职务</label>
                                                <div class="col-lg-8 col-sm-8 col-xs-12" style="margin-top:7px;">
                                                <span>${localCheak.localPersonJob}</span>

                                                </div>
                                            </div>

                                                <div class="form-group">
                                                    <label for="时间" class="col-lg-2 col-sm-2 col-xs-5 control-label">检查时间</label>
                                                    <div class="col-lg-2 col-sm-2 col-xs-12" style="margin-top:7px;">
                                                    <span>	<fmt:formatDate value='${localCheak.checkStartDate }' pattern='yyyy-MM-dd HH:mm:ss'></fmt:formatDate></span>
                                                    </div>
                                                    <label for="至" class="control-label">至</label>
                                                        <span style="margin-left:20px;"><fmt:formatDate value='${localCheak.checkEndDate }' pattern='yyyy-MM-dd HH:mm:ss'></fmt:formatDate></span>
                                                </div>

									<div class="form-group">
										<label for="行政检查通知书编号" class="col-lg-2 col-sm-2 col-xs-5 control-label">行政检查通知书编号</label>
										<div class="col-lg-8 col-sm-8 col-xs-12" style="margin-top:7px;">
											<span>${ localCheak.administrativeNoticeNumber }</span>
										</div>
									</div>

<%--									<div class="form-group">--%>
<%--										<label for="行政检查通知书附件" class="col-lg-2 col-sm-2 col-xs-5 control-label">行政检查通知书附件</label>--%>
<%--										<div class="col-lg-8 col-sm-8 col-xs-12" style="margin-top:7px;">--%>
<%--											<span>${ localCheak.attachmentFileName }</span>--%>
<%--										</div>--%>
<%--									</div>--%>

									<div class="form-group">
										<label for="行政检查通知书附件" class="col-lg-2 col-sm-2 col-xs-5 control-label">行政检查通知书附件</label>
										<div class="col-lg-8 col-sm-8 col-xs-12" style="margin-top:7px;">
											<div id="adminNoticeAttachment"></div>
<%--											<button type="button" class="btn btn-info" onclick="showFileModal(${fileId})">查看附件</button>--%>
										</div>
									</div>

<%--									<!-- 案由（Modal） -->--%>
<%--									<div class="modal fade" id="anyou" tabindex="-1" role="dialog"--%>
<%--										 aria-labelledby="myModalLabel" aria-hidden="true">--%>
<%--										<div class="modal-dialog modal-lg">--%>
<%--											<div class="modal-content"></div>--%>
<%--										</div>--%>
<%--									</div>--%>
<%--									<!-- 附件预览  -->--%>
<%--									<div class="modal fade" id="inputImgModeler" tabindex="-1" role="dialog"--%>
<%--										 aria-labelledby="myModalLabel" aria-hidden="true">--%>
<%--										<div class="modal-dialog modal-lg">--%>
<%--											<div class="modal-content">--%>

<%--											</div>--%>
<%--										</div>--%>
<%--									</div>--%>

									<!--  附件查看 -->
<%--									<div class="modal fade" id="view" tabindex="-1" role="dialog"--%>
<%--										 aria-labelledby="myModalLabel" aria-hidden="true">--%>
<%--										<div class="modal-dialog modal-lg">--%>
<%--											<div class="modal-content"></div>--%>
<%--										</div>--%>
<%--									</div>--%>



									<div class="form-group">
													<label for="告知事项" class="col-lg-2 col-sm-2 col-xs-5 control-label">告知事项</label>
													<div class="col-lg-8 col-sm-8 col-xs-12" style="line-height:2em;">

			                                           	我们是${localCheak.informDeptName }的行政执法人员，这是我们的执法证件（执法证编号：${localCheak.informLawIds }）。请过目确认：_______________今天我们依法进行检查并了解有关情况，你应当配合调查，如实提供材料，不得拒绝、阻碍、隐瞒或者提供虚假情况。如果你认为检查人与本案有利害关系，可能影响公正办案，可以申请回避，并说明理由。请确认： _____________
													</div>
												</div>



										<!-- 原有检查项显示 -->
										<c:if test ="${localCheckItemStatus !='0' && localCheak.formType != '1'}">
											<div class="container">
												<h3  class="title"><span>检查项</span></h3>
											</div>
                                          <div class="smart-widget-body text-center padding-sm">
										<div class="row"  style="word-break: break-all; work-wrap: break-word;">
											<div class="col-md-1"></div>
											<div class="col-md-10">
												<table class="table table-striped  no-margin" id ="localChickItemOnlyTable">
													<tbody id="addTr" >
												<tr v-for="(item, index) in items" :id ="'tr'+index">
													  <td   class="text-left" style="width:270px;background-color: #f7f7f7;"  :id ="'contentTr'+index">
													  <span v-if="item.isMust ==1">
													 	 <span style="color: red;">*</span> {{ item.checkItemName }}
													  </span>
													  <span v-else>
													  &nbsp; &nbsp;{{ item.checkItemName }}
													  </span>
													  </td>
											  		<td class="text-left" style="display: none;">{{ item.id }} </td>
											 	 	<td class="text-right" style="background-color: #f7f7f7;">
														<span v-if="item.checkItemStatus ==0" style="float: right;">
														<!-- 单选  -->
															<label class="radio-inline" v-if="item.checkItemResult == '1'">
															<!--  <input type="radio"  :name="'radio'+index"   checked  v-model="item.checkItemResult" v-on:click="checkItemChooseBtn(index)" value="1">  -->
															 <input type="radio"  :name="'radio'+index"   checked  v-model="item.checkItemResult"  value="1">
															 是
															</label>
															<label class="radio-inline" v-else>
															<!-- <input type="radio"  :name="'radio'+index"  value="1"  v-on:click="checkItemChooseBtn(index)" v-model="item.checkItemResult">  -->
															<input type="radio"  :name="'radio'+index"  value="1"  v-model="item.checkItemResult">
															 是
															</label>
															<c:choose>
																<c:when test="${lawObj.taskFromType =='1' }">
																	<label class="radio-inline" v-if="item.checkItemResult == '0'">
																	<!-- <input type="radio"  :name="'radio'+index"  v-on:click="checkItemChooseBtn(index)" checked  value="0" v-model="item.checkItemResult">  -->
																	<input type="radio"  :name="'radio'+index"   v-on:click="checkItemChooseBtn(index)" checked  value="0" v-model="item.checkItemResult">
																	 否</label>
																	<label class="radio-inline" v-else>
																	<!-- <input type="radio"  :name="'radio'+index" v-on:click="checkItemChooseBtn(index)"  v-model="item.checkItemResult" value="0" >  -->
																	<input type="radio"  :name="'radio'+index" v-on:click="checkItemChooseBtn(index)"   v-model="item.checkItemResult" value="0" >
																	 否
																	</label>
																</c:when>
																<c:otherwise>
																	<label class="radio-inline" v-if="item.checkItemResult == '0'">
																	<input type="radio"  :name="'radio'+index"
																	 checked  value="0" v-model="item.checkItemResult">
																	 否</label>
																	<label class="radio-inline" v-else>
																	<input type="radio"  :name="'radio'+index"   v-model="item.checkItemResult"
																	 value="0" >
																	 否
																	</label>
																</c:otherwise>
															</c:choose>
														</span>

														<span v-if="item.checkItemStatus ==4">
															<!-- 4输入(单行文本) -->
																<input type="text"  class="form-control"  :value="item.checkItemResult" v-model="item.checkItemResult">
														</span>
														<div v-if="item.checkItemStatus ==5">
															<!-- 5经纬度 -->
															        <div class="col-lg-12">
															        <!-- 这个地方经纬度调换位置是因为向右浮动导致的 -->
                                                                        <div class="col-lg-2" style="float:right; width:50px;">
                                                                        <button class="btn btn-info" type="button"
																		 		 v-on:click="createMapClick('','','',index)"
																			>定位</button>
							 											<li :id="'getPositionSuccess'+index" style="color:cornflowerblue; display:none;">坐标获取成功，请保存信息。</li>
                                                                        </div>
                                                                        <div class="col-lg-3" style="float:right;">
                                                                        <input class="form-control" :name="'gisCoordinateY'+index"
                                                                         :id="'mapWD'+index" placeholder="纬度">
                                                                        </div>
                                                                        <div class="col-lg-3" style="float:right;">
                                                                        <input class="form-control" :name="'gisCoordinateX'+index"  :id="'mapJD'+index"  placeholder="经度">
                                                                        </div>
                                                   </div>
														</div>
														<div v-if="item.checkItemStatus ==6">
															<!-- 6单一时间-->
																	<div class="col-lg-5" style="float:right;">
																	<input type="text" v-model="item.checkItemResult"
						                                            	:id="'chickOneDate'+index" class="form-control"
						                                            	readonly="readonly" placeholder="请输入时间"
						                                            	v-on:click="chickOneDate(index,item.dateType)">
						                                            </div>
														</div>
														<div class ="form-group"  v-if="item.checkItemStatus ==7">
															<!--7时间段 -->
															<!-- 这个地方经纬度调换位置是因为向右浮动导致的 -->
																    <div class="col-lg-5" style="float:right;">
																	<input type="text" v-model="item.endDateStr"
						                                            	:id="'chickTwoEndDate'+index" class="form-control"
						                                            	readonly="readonly" placeholder="请输入结束时间"
						                                            	v-on:click="chickTwoEndDate(index,item.dateType)">
						                                     </div>
																	<div class="col-lg-5" style="float:right;">
																	<input type="text" v-model="item.startDateStr"
						                                            	:id="'chickTwoStartDate'+index" class="form-control"
						                                            	readonly="readonly" placeholder="请输入开始时间"
						                                            	v-on:click="chickTwoStartDate(index,item.dateType)">
																</div>
														</div>


														<span v-if="item.checkItemStatus ==3" >
															<!--下拉-->
															<span v-if="item.sceneCheckEntryList!= null">
																<select class="form-control" v-model="item.checkItemResult" >
																	<option value="null" >请选择</option>
																	<option  v-for="(tcList1, index) in item.sceneCheckEntryList" :value="tcList1.checkEntryName" >
																	{{tcList1.checkEntryName}}
																	</option>
																</select>
															</span>
														</span>
														<span v-if="item.checkItemStatus ==2">
															<input type="hidden" name="zxjcInput222" :value="index">
															<!-- 输入-->
															<textarea  class="form-control " :name="'textarea'+index"  rows="5" v-model="item.checkItemResult" :placeholder="item.checkitemType"></textarea>
														</span>

														<div v-if="item.checkItemStatus ==1">
															<!-- 多选 -->
															 <div v-if="item.sceneCheckEntryList!= null">
																<div v-for="(tcList,index1) in item.sceneCheckEntryList"
																	class="custom-checkbox" style="width:105px; padding:0 10px 0 0;">
															        <input type="checkbox" :id="'box'+tcList.id" style="float: left;"v-model='item.checkboxResult[index1]'>
															        <!--  v-model=item.checkItemResult[index1]  -->
															        <label class="checkbox-blue" :for="'box'+tcList.id" style="float: right;"></label>
															        <span style="float: left; margin-left: 10px;">
															        {{tcList.checkEntryName}}
															        </span>
															 	</div>
															 </div>
														</div>
								 					</td>
													<td v-on:click="remarkCheckItem(index)"
														class="text-center"
														style="width: 40px; background-color: #23b7e5; cursor: pointer;"
														data-toggle="modal" data-target="#beizhu">
														<i title ='备注' class="fa fa-ellipsis-h" style="color: #FFF; font-size: 20px;"></i>
														<input  type ="text" :id="'remark'+index" style="display: none;"  v-model="item.remark" :value ='item.remark'>
													</td>
													<c:if test ="${lawObj.taskFromType =='1' }">
													  <td class="text-center" style="width:33px;  background-color:#f7f7f7; " >
													 	 <label v-if="item.behId != '' ">
															<label v-if="item.behId != null " style="width:10px; background-color:#f7f7f7;cursor:pointer;" >
															 <i class="fa fa-book"  v-on:click="bevguide(index)"  style="color:#23b7e5; font-size:20px;"></i>
															</label>
														</label>
														</td>
													</c:if>
												 </tr>
													</tbody>
												</table>
                                                <div class="padding-sm" style="text-align:left;">
                                               <%--  <c:if test="${lawObj.taskFromType =='1' }">
                                                <button type="button" class="btn btn-info" id ="saveLocalItemBtn" data-toggle="modal" data-target="#xzjcx">新增检查项</button>
                                                </c:if> --%>
                                                </div>
											</div>
											<div class="col-md-1"></div>
										</div>
									</div>
									</c:if>
									<!-- 原有检查项显示结束 -->

									<!-- 环境监管一件事数据显示 -->
									<c:if test="${localCheak.formType == '1'}">
										<div class="container">
											<h3 class="title"><span>环境监管一件事</span></h3>
										</div>
										<div class="smart-widget-body padding-sm">
											<div class="row">
												<div class="col-md-1"></div>
												<div class="col-md-10">
													<!-- 环境监管一件事树形结构（只读模式） -->
													<div id="envSupervisionReadOnlyForm">
														<div class="panel-group" id="checkItemAccordionReadOnly" role="tablist" aria-multiselectable="true">
															<c:forEach items="${checkItemTree}" var="parentItem" varStatus="parentStatus">
																<div class="panel panel-default">
																	<!-- 一级菜单标题 -->
																	<div class="panel-heading" role="tab" id="headingReadOnly${parentStatus.index}">
																		<h4 class="panel-title">
																			<a role="button" data-toggle="collapse" data-parent="#checkItemAccordionReadOnly"
																			   href="#collapseReadOnly${parentStatus.index}" aria-expanded="true"
																			   aria-controls="collapseReadOnly${parentStatus.index}">
																				${parentItem.itemName}
																				<i class="fa fa-chevron-down collapse-icon"></i>
																			</a>
																		</h4>
																	</div>

																	<!-- 二级表格内容 -->
																	<div id="collapseReadOnly${parentStatus.index}" class="panel-collapse collapse in"
																		 role="tabpanel" aria-labelledby="headingReadOnly${parentStatus.index}">
																		<div class="panel-body">
																			<div class="table-responsive">
																				<table class="table table-bordered table-hover">
																					<thead class="bg-light-grey">
																						<tr>
																							<th width="40%" class="text-center">检查要点</th>
																							<th width="30%" class="text-center">检查结果</th>
																							<th width="30%" class="text-center">问题简述</th>
																						</tr>
																					</thead>
																					<tbody>
																						<c:forEach items="${parentItem.children}" var="childItem" varStatus="childStatus">
																							<tr>
																								<td class="text-left">${childItem.itemName}</td>
																								<td class="text-center">
																									<!-- 这里需要根据envSupervisionItems数据显示检查结果 -->
																									<c:set var="currentResult" value="" />
																									<c:set var="currentProblemDesc" value="" />
																									<c:forEach items="${envSupervisionItems}" var="envItem">
																										<c:set var="expectedConfigId" value="${parentStatus.index}_${childStatus.index}" />
																										<c:if test="${envItem.configItemId == expectedConfigId}">
																											<c:set var="currentResult" value="${envItem.checkItemResult}" />
																											<c:set var="currentProblemDesc" value="${envItem.problemDesc}" />
																										</c:if>
																									</c:forEach>
																									<c:choose>
																										<c:when test="${currentResult == '0'}">
																											<span class="label label-success">合格</span>
																										</c:when>
																										<c:when test="${currentResult == '1'}">
																											<span class="label label-danger">不合格</span>
																										</c:when>
																										<c:when test="${currentResult == '2'}">
																											<span class="label label-default">不涉及</span>
																										</c:when>
																										<c:otherwise>
																											<span class="label label-default">未检查</span>
																										</c:otherwise>
																									</c:choose>
																								</td>
																								<td class="text-left">
																									<c:out value="${currentProblemDesc}" default="-" />
																								</td>
																							</tr>
																						</c:forEach>
																					</tbody>
																				</table>
																			</div>
																		</div>
																	</div>
																</div>
															</c:forEach>
														</div>
													</div>
												</div>
												<div class="col-md-1"></div>
											</div>
										</div>
									</c:if>
									<!-- 环境监管一件事数据显示结束 -->

<%-- 									<div class="form-group">
										<label class="col-lg-2 col-sm-2 col-xs-5 control-label" >是否发现违法行为？</label>
										<!-- isIllegalcatCodeList -->
										<div style="margin-top: 7px;word-wrap:break-word;word-break:break-all;">
										<c:choose>
											<c:when test="${localCheak.isIllegalactCode  ==2 }">
												疑似
											</c:when>
											<c:when test="${localCheak.isIllegalactCode  ==0 }">
												已发现
											</c:when>
											<c:when test="${localCheak.isIllegalactCode  ==1 }">
												未发现
											</c:when>
										</c:choose>
										</div>
									</div>
                                            <div class="form-group">
                                                <label class="col-lg-2 col-sm-2 col-xs-5 control-label">监察小结</label>
                                                <div class="col-md-6 text-left" style="margin-top: 7px;word-wrap:break-word;word-break:break-all;">
                                                ${ localCheak.checkSummary }
                                                </div>
                                            </div> --%>


                            </div>
                            <div class="modal-footer">
                                            <c:if test="${localCheak.id!=null && localCheak.id!=''}">
                                                <button type="button" class="btn btn-info" id ="booksDown">下载文书</button>
                                                <input type="hidden" id="docUrl" value="${localCheak.docUrl }">
                                                <button type="button" class="btn btn-info" onclick ="shareBooksBtn()">分享文书</button>
                                                <button type="button" class="btn btn-info" data-toggle="modal" data-target="#cksmj"
                                                    data-remote="${webpath}/localExamine/cksmj?localCheckId=${localCheak.id}&lsFlag=0">查看扫描件</button>
                                            </c:if>
                                            </div>
                            </div>
                    </div>

                </div>
            </div>
                <!--第三层任务办理row-->

              	</div>
            </div>

      <!-- 检查项备注（Modal） -->
        <div class="modal fade" id="beizhu" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                        <h4 class="modal-title" id="myModalLabel">检查备注说明</h4>
                    </div>
                    <div class="modal-body">
                        <div class="smart-widget-body">
                            <form class="form-horizontal">
                                <div class="form-group">
                                	<div class="col-lg-12">
                                    <label for="exampleInputEmail1">备注说明检查情况及存在的问题</label>
                                    <textarea class="form-control" id="remarkCheckItemText" rows="5"></textarea>
                                    </div>
                                    <input type= "hidden" id ="localCheckitemId" name ="localCheckitemId">
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                    </div>

                </div>
            </div>
        </div>
          <!-- 查看扫描件（Modal） -->
	<div class="modal fade" id="cksmj" tabindex="-1" role="dialog"
		aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">

			</div>
		</div>
	</div>
	<!-- ./查看扫描件（Modal） -->
	<!-- 二级预览模态框（Modal） -->
		<div class="modal fade" id="inputImgModeler" tabindex="-1" role="dialog"
		   aria-labelledby="fileModalLabel" aria-hidden="true">
			   <div class="modal-dialog  modal-lg">
		        <div class="modal-content">
		        </div>
		      </div>
		</div>
	<!-- ./二级预览模态框（Modal） -->
	<!-- 分享文书模态框start -->
		 <div class="modal fade" id="shareBookModel" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		  <div class="modal-dialog" role="document">
		    <div class="modal-content">
		    	<div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"
                        aria-hidden="true">&times;</button>
                    <h4 class="modal-title" >分享文书</h4>
                </div>
		       <div class="modal-body" >
               	<div class="pricing-value value" data-toggle="modal" data-target="#inputImgModeler" style="background-image:url(${webpath }/static/img/QRcode_backgroup.png); width:300px; height:345px; padding:0 auto; margin:0 auto;" align="center">
		    	 <div  align="center" id="qrcode" ></div>
      			 <!-- <img id="getval"/>  -->
      			 <div id="getval" align="center" style=" margin:0; padding:110px 0 0 30px;"></div>
      			 <div style="width: 80px; height: 80px; position:absolute; margin:-100px 0 0 125px;"><img id="qrCodeIco" src="${webpath }/static/img/qrLogo.png" /></div>
                 </div>
		       </div>
		      <div class="modal-footer">
		        <!--<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>-->
		      </div>
		    </div>
		  </div>
		</div>
		<!-- 分享文书模态框end -->
        <!-- ./检查项备注（Modal） -->
		<div class="modal fade" id="wfxwzd" tabindex="-1" role="dialog"
			aria-labelledby="myModalLabel" aria-hidden="true">
			<div class="modal-dialog modal-lg">
				<div class="modal-content"></div>
			</div>
		</div>
      	<script language="javascript">

      	var dataArr = new Array();
      	function TestXC(behId){
      	  	var options = {
					remote:WEBPATH+'/localExamine/behaviorguide?behId='+behId
				  };
				$('#wfxwzd').modal(options);
      	}
    	var lawObjectType  = $("#lawObjectType").val();
		var taskId = $("#taskId").val();
		var localCheakId = $("#localCheakId").val();
			//查询默认检查项信息
			if(localCheakId != null &&localCheakId !=''){
				$.ajax({
				type : "POST",
				url : WEBPATH + '/localExamine/checkItemList',
				data : {
					lawObjectType :  lawObjectType,
					localCheakId:localCheakId,
					status:1,
					taskId:taskId
				},//  form law object
				async : false,
				error : function(request) {
					swal("错误!","获取模板项失败！", "error");
				},
				success : function(data) {
					dataArr=data.list;
				}
			});
			}
		  	var vue = new Vue({
				  el: '#localChickItemOnlyTable',
				  data: {
				    items:dataArr,
				  },
				  methods: {
					  /* 打开备注操作  */
					  /* 打开备注操作  */
					  remarkCheckItem: function (index) {
						    var remark = "remark".concat(index);
							var remarkTemp = $("#".concat(remark)).val();
							$("#localCheckitemId").val(this.items[index].id);
							$("#localCheckitemIndex").val(index);
							$("#remarkCheckItemText").val(this.items[index].remark);
					    },
					    bevguide:function(index){
					    	  var behId =this.items[index].behId;
					    	  var options = {
										remote:WEBPATH+'/localExamine/behaviorguide?behId='+behId
									  };
									$('#wfxwzd').modal(options);
					    },
					    bevguide:function(index){
					    	  var behId =this.items[index].behId;
					    	  var options = {
										remote:WEBPATH+'/localExamine/behaviorguide?behId='+behId
									  };
									$('#wfxwzd').modal(options);
					    },
				  }
			})
		//修改查看检查项备注
		function remarkCheckItem(localCheckitemId) {
			$("#remarkCheckItemText").val("");
			$("#localCheckitemId").val("");
			$.ajax({
				cache : true,
				type : "POST",
				url : WEBPATH + '/localExamine/checkItemRemark',
				data : {
					localCheckitemId : localCheckitemId
				},//  form law object
				async : false,
				error : function(request) {
					swal("错误!","添加检查项备注失败！", "error");
				},
				success : function(data) {
					if (data.meta.result == "success") {
						$("#remarkCheckItemText").val(data.data.remark);
						$("#localCheckitemId").val(localCheckitemId);
					}
				}
			});

		}
      	//下载文书
      		if(vue.items != null && vue.items.length>0){
			 for(var i=0;i<vue.items.length;i++){
				 if(vue.items[i].checkItemStatus =='5'){
						//经度纬度绑定值
						$("#mapJD"+i).val(vue.items[i].gisCoordinateX);
						$("#mapWD"+i).val(vue.items[i].gisCoordinateY);
				 }

			 }
		 }

		$("#booksDown").click(
				function() {
					var localCheckId = $("#localCheakId").val();
					window.location.href = WEBPATH
							+ '/localExamine/booksDown?localCheckId='
							+ localCheckId;
				});

			</script>

			<script type="text/javascript">
			     //分享文书
			     function shareBooksBtn(){
			    	var docUrl = $("#docUrl").val();
			 		var base = new Base64();
					var result = base.encode( FASTDFS_ADDR +docUrl);
			    	var url = SERVER_ADDR + "qrCodeSharing/sharePage?url="+ result;
			    	if (docUrl != '' && docUrl != null) {
			    		$('#qrcode').qrcode({
			                render: 'canvas',
			                text: url,
			                height: 210,
			                width: 210,
			                //logo图片地址${webpath }/static/img/qrLogo.png
			                //src: '${webpath }/static/img/qrLogo.png'
			            }).hide();

						$("#shareBookModel").modal('show');

					} else {
						swal({
							title : "提示！",
							text : "无文书可分享！",
							type : "error"
						})
					}
			    	//从 canvas 提取图片 image
			        function convertCanvasToImage(canvas) {
			            //新Image对象，可以理解为DOM
			            var image = new Image();
			            // canvas.toDataURL 返回的是一串Base64编码的URL，当然,浏览器自己肯定支持
			            // 指定格式 PNG
			            image.src = canvas.toDataURL("image/png");
			            return image;
			        }

			        $('#getval').empty();
			        //获取网页中的canvas对象
			        var mycanvas1=document.getElementsByTagName('canvas')[0];
			        //将转换后的img标签插入到html中
			        var img=convertCanvasToImage(mycanvas1);

			        $('#getval').append(img);//imagQrDiv表示你要插入的容器id
			      //控制Logo图标的位置
			        var margin = ($("#getval").height() - $("#qrCodeIco").height()) / 2;
			        $("#qrCodeIco").css("margin", margin);
			     }


			</script>


	<script type="text/javascript">
		// 定义 hisShowFiles 函数
	$(document).ready(function() {
		var localCheckId = $('#localCheakId').val();
		if (localCheckId) {
			$.ajax({
				url: '/localExamine/attSee',
				type: 'POST',
				data: { localCheckId: localCheckId },
				success: function(response) {
					if (response.code === '200') {
						console.log('attSee的response:', response.data)
						var file = response.data;
						var name = file.fileName;
						var id = file.id;
						var name1 = "";
						if(name.length>5){
							var sName = name.split(".");
							var strName = name.substring(0,5);
							name1 = strName+"..."+sName[sName.length-1];
						}
						if (file) {
							// 在这里处理文件信息，例如生成下载链接或预览链接
							// displayAttachment(file);
							if(file.fileType=='1'){
								//文件类型：1图片，2PDF
								console.log("data[i].fileType=='1'");
								$("#adminNoticeAttachment").append('<div class="col-md-3 col-sm-3"><div class="pricing-widget clean-pricing"><a href="javascript:void(0)">' +
										'<img style="height: 130px;" data-toggle="modal" data-remote="${webpath}/localExamine/showFileModal?id='+id+'" '
										+		'data-target="#inputImgModeler" src="'+'${FASTDFS_ADDR}/'+file.fileUrl+'"/></a></div><div title="'+name+'">'+name1+'</div></div>');
							}else if(file.fileType=='2'){
								console.log("data[i].fileType=='2'");
								console.log("name>>>>>>>>>>>>>>>>>>>>" + name);
								$("#adminNoticeAttachment").append('<div class="col-md-3 col-sm-3"><div class="pricing-widget clean-pricing">' +
										'<a href="javascript:void(0)"><img style="height: 130px;" data-toggle="modal" data-remote="${webpath}/localExamine/showFileModal?id='+id+'" ' +
										'data-target="#inputImgModeler" src="'+'${webpath}/static/img/pdf-thumb.jpg'+'"/></a></div><div title="'+name+'">'+name1+'</div></div>');
							}

						} else {
							$('#adminNoticeAttachment').html('无附件');
						}
					} else {
						$('#adminNoticeAttachment').html('获取附件失败');
					}
				},
				error: function() {
					$('#adminNoticeAttachment').html('请求失败');
				}
			});
		} else {
			$('#adminNoticeAttachment').html('无附件');
		}
	});

	</script>



<%--<script type="text/javascript">--%>
<%--	// 展示文件模态框--%>
<%--	function showFileModal(fileId) {--%>
<%--		$.ajax({--%>
<%--			url: '/localExamine/showFileModal',--%>
<%--			type: 'GET',--%>
<%--			data: { fileId: fileId },--%>
<%--			success: function(response) {--%>
<%--				// 显示模态框--%>
<%--				$('#fileModal').html(response); // 假设模态框的容器ID为 "fileModal"--%>
<%--				$('#fileModal').modal('show');--%>
<%--			},--%>
<%--			error: function() {--%>
<%--				alert('请求失败');--%>
<%--			}--%>
<%--		});--%>
<%--	}--%>


<%--</script>--%>


</body>
</html>
